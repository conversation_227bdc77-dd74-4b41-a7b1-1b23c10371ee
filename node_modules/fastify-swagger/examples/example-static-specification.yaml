openapi: 3.0.0
info:
  description: "Test swagger specification"
  version: "1.0.0"
  title: "Test swagger specification"
  contact:
    email: "<EMAIL>"
servers:
  - url: http://localhost:3000/
    description: Localhost (uses test data)
paths:
  /status:
    get:
      description: Status route, so we can check if server is alive
      tags:
        - Status
      responses:
        200:
          description: 'Server is alive'
          content:
            application/json:
              schema:
                type: object
                properties:
                  health:
                     type: boolean
                  date:
                     type: string
                example:
                 health: true 
                 date: "2018-02-19T15:36:46.758Z"