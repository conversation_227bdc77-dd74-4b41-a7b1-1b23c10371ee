cmd_Release/obj.target/snappy/deps/snappy/snappy-1.1.7/snappy-sinksource.o := c++ -o Release/obj.target/snappy/deps/snappy/snappy-1.1.7/snappy-sinksource.o ../deps/snappy/snappy-1.1.7/snappy-sinksource.cc '-DNODE_GYP_MODULE_NAME=snappy' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DHAVE_CONFIG_H=1' -I/Users/<USER>/Library/Caches/node-gyp/22.16.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/22.16.0/src -I/Users/<USER>/Library/Caches/node-gyp/22.16.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/22.16.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/22.16.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/22.16.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/22.16.0/deps/v8/include -I../deps/snappy/mac -I../deps/snappy/snappy-1.1.7  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=10.9 -arch x86_64 -Wall -Wendif-labels -W -Wno-unused-parameter -Wno-sign-compare -Wno-unused-function -std=gnu++17 -stdlib=libc++ -fno-rtti -fno-exceptions -MMD -MF ./Release/.deps/Release/obj.target/snappy/deps/snappy/snappy-1.1.7/snappy-sinksource.o.d.raw   -c
Release/obj.target/snappy/deps/snappy/snappy-1.1.7/snappy-sinksource.o: \
  ../deps/snappy/snappy-1.1.7/snappy-sinksource.cc \
  ../deps/snappy/snappy-1.1.7/snappy-sinksource.h
../deps/snappy/snappy-1.1.7/snappy-sinksource.cc:
../deps/snappy/snappy-1.1.7/snappy-sinksource.h:
