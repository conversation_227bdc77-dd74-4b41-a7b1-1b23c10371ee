{"version": 3, "sources": ["../../src/metadata/MetadataStorage.ts"], "names": [], "mappings": "AAGA,OAAO,EAAC,qCAAqC,EAAC,MAAM,4DAA4D,CAAC;AAEjH;;;GAGG;AACH,MAAM,UAAU,kBAAkB;IAC9B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAC9B,MAAc,CAAC,MAAM,GAAG,MAAM,CAAC;KACnC;IACD,IAAI,CAAE,MAAc,CAAC,6BAA6B;QAC7C,MAAc,CAAC,6BAA6B,GAAG,IAAI,eAAe,EAAE,CAAC;IAE1E,OAAQ,MAAc,CAAC,6BAA6B,CAAC;AACzD,CAAC;AAED;;GAEG;AACH;IAAA;QAEI,4EAA4E;QAC5E,qBAAqB;QACrB,4EAA4E;QAEpE,wBAAmB,GAAyB,EAAE,CAAC;QAC/C,wBAAmB,GAAyB,EAAE,CAAC;IAkG3D,CAAC;IAhGG,sBAAI,kDAAqB;aAAzB;YACI,OAAO,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;QAC7C,CAAC;;;OAAA;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,6CAAmB,GAAnB,UAAoB,MAAwB;QAA5C,iBAGC;QAFG,IAAM,mBAAmB,GAAG,IAAI,qCAAqC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC1F,mBAAmB,CAAC,OAAO,CAAC,UAAA,kBAAkB,IAAI,OAAA,KAAI,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,EAA9C,CAA8C,CAAC,CAAC;IACtG,CAAC;IAED;;OAEG;IACH,+CAAqB,GAArB,UAAsB,QAA4B;QAC9C,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,+CAAqB,GAArB,UAAsB,QAA4B;QAC9C,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,6CAAmB,GAAnB,UAAoB,QAA8B;QAC9C,IAAM,OAAO,GAAqD,EAAE,CAAC;QACrE,QAAQ,CAAC,OAAO,CAAC,UAAA,QAAQ;YACrB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAC/B,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YACxC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,sDAA4B,GAA5B,UAA6B,iBAA2B,EAAE,YAAoB,EAAE,MAAiB;QAE7F,6CAA6C;QAC7C,IAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAA,QAAQ;YAC9D,IAAI,QAAQ,CAAC,MAAM,KAAK,iBAAiB,IAAI,QAAQ,CAAC,MAAM,KAAK,YAAY;gBACzE,OAAO,KAAK,CAAC;YACjB,IAAI,QAAQ,CAAC,MAAM;gBACf,OAAO,IAAI,CAAC;YAChB,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;gBAC3B,OAAO,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAA5B,CAA4B,CAAC,CAAC;YAE5F,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,sCAAsC;QACtC,IAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAA,QAAQ;YAC/D,gHAAgH;YAChH,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,QAAQ;gBACnC,OAAO,KAAK,CAAC;YACjB,IAAI,QAAQ,CAAC,MAAM,KAAK,iBAAiB;gBACrC,OAAO,KAAK,CAAC;YACjB,IAAI,QAAQ,CAAC,MAAM,YAAY,QAAQ;gBACnC,CAAC,CAAC,iBAAiB,CAAC,SAAS,YAAa,QAAQ,CAAC,MAAmB,CAAC;gBACvE,OAAO,KAAK,CAAC;YACjB,IAAI,QAAQ,CAAC,MAAM;gBACf,OAAO,IAAI,CAAC;YAChB,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;gBAC3B,OAAO,QAAQ,CAAC,MAAM,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAA5B,CAA4B,CAAC,CAAC;YAE5F,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,2FAA2F;QAC3F,IAAM,wBAAwB,GAAG,kBAAkB,CAAC,MAAM,CAAC,UAAA,iBAAiB;YACxE,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAA,gBAAgB;gBAC3C,OAAQ,gBAAgB,CAAC,YAAY,KAAK,iBAAiB,CAAC,YAAY;oBAChE,gBAAgB,CAAC,IAAI,KAAK,iBAAiB,CAAC,IAAI,CAAC;YAC7D,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,OAAO,iBAAiB,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,uDAA6B,GAA7B,UAA8B,MAAgB;QAC1C,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,MAAM,KAAK,MAAM,EAA1B,CAA0B,CAAC,CAAC;IACnF,CAAC;IAEL,sBAAC;AAAD,CAzGA,AAyGC,IAAA", "file": "MetadataStorage.js", "sourcesContent": ["import {ValidationMetadata} from \"./ValidationMetadata\";\nimport {ConstraintMetadata} from \"./ConstraintMetadata\";\nimport {ValidationSchema} from \"../validation-schema/ValidationSchema\";\nimport {ValidationSchemaToMetadataTransformer} from \"../validation-schema/ValidationSchemaToMetadataTransformer\";\n\n/**\n * Gets metadata storage.\n * Metadata storage follows the best practices and stores metadata in a global variable.\n */\nexport function getMetadataStorage(): MetadataStorage {\n    if (typeof window !== \"undefined\") {\n        (window as any).global = window;\n    }\n    if (!(global as any).classValidatorMetadataStorage)\n        (global as any).classValidatorMetadataStorage = new MetadataStorage();\n\n    return (global as any).classValidatorMetadataStorage;\n}\n\n/**\n * Storage all metadatas.\n */\nexport class MetadataStorage {\n\n    // -------------------------------------------------------------------------\n    // Private properties\n    // -------------------------------------------------------------------------\n\n    private validationMetadatas: ValidationMetadata[] = [];\n    private constraintMetadatas: ConstraintMetadata[] = [];\n\n    get hasValidationMetaData() {\n        return !!this.validationMetadatas.length;\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Adds a new validation metadata.\n     */\n    addValidationSchema(schema: ValidationSchema) {\n        const validationMetadatas = new ValidationSchemaToMetadataTransformer().transform(schema);\n        validationMetadatas.forEach(validationMetadata => this.addValidationMetadata(validationMetadata));\n    }\n\n    /**\n     * Adds a new validation metadata.\n     */\n    addValidationMetadata(metadata: ValidationMetadata) {\n        this.validationMetadatas.push(metadata);\n    }\n\n    /**\n     * Adds a new constraint metadata.\n     */\n    addConstraintMetadata(metadata: ConstraintMetadata) {\n        this.constraintMetadatas.push(metadata);\n    }\n\n    /**\n     * Groups metadata by their property names.\n     */\n    groupByPropertyName(metadata: ValidationMetadata[]): { [propertyName: string]: ValidationMetadata[] } {\n        const grouped: { [propertyName: string]: ValidationMetadata[] } = {};\n        metadata.forEach(metadata => {\n            if (!grouped[metadata.propertyName])\n                grouped[metadata.propertyName] = [];\n            grouped[metadata.propertyName].push(metadata);\n        });\n        return grouped;\n    }\n\n    /**\n     * Gets all validation metadatas for the given object with the given groups.\n     */\n    getTargetValidationMetadatas(targetConstructor: Function, targetSchema: string, groups?: string[]): ValidationMetadata[] {\n\n        // get directly related to a target metadatas\n        const originalMetadatas = this.validationMetadatas.filter(metadata => {\n            if (metadata.target !== targetConstructor && metadata.target !== targetSchema)\n                return false;\n            if (metadata.always)\n                return true;\n            if (groups && groups.length > 0)\n                return metadata.groups && !!metadata.groups.find(group => groups.indexOf(group) !== -1);\n\n            return true;\n        });\n\n        // get metadatas for inherited classes\n        const inheritedMetadatas = this.validationMetadatas.filter(metadata => {\n            // if target is a string it's means we validate agains a schema, and there is no inheritance support for schemas\n            if (typeof metadata.target === \"string\")\n                return false;\n            if (metadata.target === targetConstructor)\n                return false;\n            if (metadata.target instanceof Function &&\n                !(targetConstructor.prototype instanceof (metadata.target as Function)))\n                return false;\n            if (metadata.always)\n                return true;\n            if (groups && groups.length > 0)\n                return metadata.groups && !!metadata.groups.find(group => groups.indexOf(group) !== -1);\n\n            return true;\n        });\n\n        // filter out duplicate metadatas, prefer original metadatas instead of inherited metadatas\n        const uniqueInheritedMetadatas = inheritedMetadatas.filter(inheritedMetadata => {\n            return !originalMetadatas.find(originalMetadata => {\n                return  originalMetadata.propertyName === inheritedMetadata.propertyName &&\n                        originalMetadata.type === inheritedMetadata.type;\n            });\n        });\n\n        return originalMetadatas.concat(uniqueInheritedMetadatas);\n    }\n\n    /**\n     * Gets all validator constraints for the given object.\n     */\n    getTargetValidatorConstraints(target: Function): ConstraintMetadata[] {\n        return this.constraintMetadatas.filter(metadata => metadata.target === target);\n    }\n\n}\n"], "sourceRoot": ".."}