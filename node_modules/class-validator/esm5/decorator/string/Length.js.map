{"version": 3, "sources": ["../../src/decorator/string/Length.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,CAAC,IAAM,MAAM,GAAG,QAAQ,CAAC;AAE/B;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc,EAAE,GAAW,EAAE,GAAY;IAC5D,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,GAAG,KAAA,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC;AAChF,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,GAAW,EAAE,GAAY,EAAE,iBAAqC;IACnF,OAAO,UAAU,CACb;QACI,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;QACvB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAvD,CAAuD;YAClF,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,EAAE,IAAI;gBACb,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC;gBACtF,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC;gBACtF,IAAI,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;oBACzE,OAAO,UAAU,GAAG,mEAAmE,CAAC;iBAC3F;qBAAM,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;oBACjE,OAAO,UAAU,GAAG,oEAAoE,CAAC;iBAC5F;gBACD,OAAO,UAAU,GAAG,6GAA6G,CAAC;YACtI,CAAC,EACD,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "Length.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport validator from \"validator\";\n\nexport const LENGTH = \"length\";\n\n/**\n * Checks if the string's length falls in a range. Note: this function takes into account surrogate pairs.\n * If given value is not a string, then it returns false.\n */\nexport function length(value: unknown, min: number, max?: number): boolean {\n    return typeof value === \"string\" && validator.isLength(value, { min, max });\n}\n\n/**\n * Checks if the string's length falls in a range. Note: this function takes into account surrogate pairs.\n * If given value is not a string, then it returns false.\n */\nexport function Length(min: number, max?: number, validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: LENGTH,\n            constraints: [min, max],\n            validator: {\n                validate: (value, args) => length(value, args.constraints[0], args.constraints[1]),\n                defaultMessage: buildMessage(\n                    (eachPrefix, args) => {\n                        const isMinLength = args.constraints[0] !== null && args.constraints[0] !== undefined;\n                        const isMaxLength = args.constraints[1] !== null && args.constraints[1] !== undefined;\n                        if (isMinLength && (!args.value || args.value.length < args.constraints[0])) {\n                            return eachPrefix + \"$property must be longer than or equal to $constraint1 characters\";\n                        } else if (isMaxLength && (args.value.length > args.constraints[1])) {\n                            return eachPrefix + \"$property must be shorter than or equal to $constraint2 characters\";\n                        }\n                        return eachPrefix + \"$property must be longer than or equal to $constraint1 and shorter than or equal to $constraint2 characters\";\n                    },\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}