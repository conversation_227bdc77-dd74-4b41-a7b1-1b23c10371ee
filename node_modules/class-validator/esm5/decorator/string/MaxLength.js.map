{"version": 3, "sources": ["../../src/decorator/string/MaxLength.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,CAAC,IAAM,UAAU,GAAG,WAAW,CAAC;AAEtC;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,KAAc,EAAE,GAAW;IACjD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC;AACnF,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,GAAW,EAAE,iBAAqC;IACxE,OAAO,UAAU,CACb;QACI,IAAI,EAAE,UAAU;QAChB,WAAW,EAAE,CAAC,GAAG,CAAC;QAClB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAArC,CAAqC;YAChE,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,oEAAoE,EAAjF,CAAiF,EACjG,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "MaxLength.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport validator from \"validator\";\n\nexport const MAX_LENGTH = \"maxLength\";\n\n/**\n * Checks if the string's length is not more than given number. Note: this function takes into account surrogate pairs.\n * If given value is not a string, then it returns false.\n */\nexport function maxLength(value: unknown, max: number) {\n    return typeof value === \"string\" && validator.isLength(value, { min: 0, max });\n}\n\n/**\n * Checks if the string's length is not more than given number. Note: this function takes into account surrogate pairs.\n * If given value is not a string, then it returns false.\n */\nexport function MaxLength(max: number, validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: MAX_LENGTH,\n            constraints: [max],\n            validator: {\n                validate: (value, args) => maxLength(value, args.constraints[0]),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be shorter than or equal to $constraint1 characters\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}