{"version": 3, "sources": ["../../src/decorator/string/IsRgbColor.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,CAAC,IAAM,YAAY,GAAG,YAAY,CAAC;AAEzC;;;;GAIG;AACH,MAAM,UAAU,UAAU,CAAC,KAAc,EAAE,oBAA8B;IACrE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;AAC1F,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,UAAU,CAAC,oBAA8B,EAAE,iBAAqC;IAC5F,OAAO,UAAU,CACb;QACI,IAAI,EAAE,YAAY;QAClB,WAAW,EAAE,CAAC,oBAAoB,CAAC;QACnC,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAtC,CAAsC;YACjE,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,6BAA6B,EAA1C,CAA0C,EAC1D,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsRgbColor.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport validator from \"validator\";\n\nexport const IS_RGB_COLOR = \"isRgbColor\";\n\n/**\n* Check if the string is a rgb or rgba color.\n * `includePercentValues` defaults to true. If you don't want to allow to set rgb or rgba values with percents, like rgb(5%,5%,5%), or rgba(90%,90%,90%,.3), then set it to false.\n * If given value is not a string, then it returns false.\n */\nexport function isRgbColor(value: unknown, includePercentValues?: boolean): boolean {\n    return typeof value === \"string\" && validator.isRgbColor(value, includePercentValues);\n}\n\n/**\n * Check if the string is a rgb or rgba color.\n * `includePercentValues` defaults to true. If you don't want to allow to set rgb or rgba values with percents, like rgb(5%,5%,5%), or rgba(90%,90%,90%,.3), then set it to false.\n * If given value is not a string, then it returns false.\n */\nexport function IsRgbColor(includePercentValues?: boolean, validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_RGB_COLOR,\n            constraints: [includePercentValues],\n            validator: {\n                validate: (value, args) => isRgbColor(value, args.constraints[0]),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be RGB color\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}