{"version": 3, "sources": ["../../src/decorator/string/IsVariableWidth.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,CAAC,IAAM,iBAAiB,GAAG,iBAAiB,CAAC;AAEnD;;;GAGG;AACH,MAAM,UAAU,eAAe,CAAC,KAAc;IAC1C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AACzE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,eAAe,CAAC,iBAAqC;IACjE,OAAO,UAAU,CACb;QACI,IAAI,EAAE,iBAAiB;QACvB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,eAAe,CAAC,KAAK,CAAC,EAAtB,CAAsB;YACjD,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,+DAA+D,EAA5E,CAA4E,EAC5F,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsVariableWidth.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport validator from \"validator\";\n\nexport const IS_VARIABLE_WIDTH = \"isVariableWidth\";\n\n/**\n * Checks if the string contains variable-width chars.\n * If given value is not a string, then it returns false.\n */\nexport function isVariableWidth(value: unknown): boolean {\n    return typeof value === \"string\" && validator.isVariableWidth(value);\n}\n\n/**\n * Checks if the string contains variable-width chars.\n * If given value is not a string, then it returns false.\n */\nexport function IsVariableWidth(validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_VARIABLE_WIDTH,\n            validator: {\n                validate: (value, args) => isVariableWidth(value),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must contain a full-width and half-width characters\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}