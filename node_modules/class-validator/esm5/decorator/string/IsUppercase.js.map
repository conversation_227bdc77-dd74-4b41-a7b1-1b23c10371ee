{"version": 3, "sources": ["../../src/decorator/string/IsUppercase.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,CAAC,IAAM,YAAY,GAAG,aAAa,CAAC;AAE1C;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,KAAc;IACtC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACrE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,iBAAqC;IAC7D,OAAO,UAAU,CACb;QACI,IAAI,EAAE,YAAY;QAClB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,WAAW,CAAC,KAAK,CAAC,EAAlB,CAAkB;YAC7C,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,6BAA6B,EAA1C,CAA0C,EAC1D,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsUppercase.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport validator from \"validator\";\n\nexport const IS_UPPERCASE = \"isUppercase\";\n\n/**\n * Checks if the string is uppercase.\n * If given value is not a string, then it returns false.\n */\nexport function isUppercase(value: unknown): boolean {\n    return typeof value === \"string\" && validator.isUppercase(value);\n}\n\n/**\n * Checks if the string is uppercase.\n * If given value is not a string, then it returns false.\n */\nexport function IsUppercase(validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_UPPERCASE,\n            validator: {\n                validate: (value, args) => isUppercase(value),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be uppercase\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}