{"version": 3, "sources": ["../../src/decorator/string/Matches.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,CAAC,IAAM,OAAO,GAAG,SAAS,CAAC;AAQjC,MAAM,UAAU,OAAO,CAAC,KAAa,EAAE,OAAwB,EAAE,SAAkB;IAC/E,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,OAAyB,EAAE,SAAS,CAAC,CAAC;AACvG,CAAC;AAQD,MAAM,UAAU,OAAO,CAAC,OAAwB,EAAE,4BAAyD,EAAE,iBAAqC;IAC9I,IAAI,SAAiB,CAAC;IACtB,IAAI,4BAA4B,IAAI,4BAA4B,YAAY,MAAM,IAAI,CAAC,iBAAiB,EAAE;QACtG,iBAAiB,GAAG,4BAAiD,CAAC;KACzE;SAAM;QACH,SAAS,GAAG,4BAAsC,CAAC;KACtD;IAED,OAAO,UAAU,CACb;QACI,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;QACjC,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAxD,CAAwD;YACnF,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,EAAE,IAAI,IAAK,OAAA,UAAU,GAAG,sDAAsD,EAAnE,CAAmE,EACzF,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "Matches.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport validator from \"validator\";\n\nexport const MATCHES = \"matches\";\n\n/**\n * Checks if string matches the pattern. Either matches('foo', /foo/i).\n * If given value is not a string, then it returns false.\n */\nexport function matches(value: string, pattern: RegExp): boolean;\nexport function matches(value: string, pattern: string, modifiers: string): boolean;\nexport function matches(value: string, pattern: RegExp | string, modifiers?: string): boolean {\n    return typeof value === \"string\" && validator.matches(value, pattern as unknown as any, modifiers);\n}\n\n/**\n * Checks if string matches the pattern. Either matches('foo', /foo/i)\n * If given value is not a string, then it returns false.\n */\nexport function Matches(pattern: RegExp, validationOptions?: ValidationOptions): PropertyDecorator;\nexport function Matches(pattern: string, modifiers?: string, validationOptions?: ValidationOptions): PropertyDecorator;\nexport function Matches(pattern: RegExp | string, modifiersOrAnnotationOptions?: string | ValidationOptions, validationOptions?: ValidationOptions): PropertyDecorator {\n    let modifiers: string;\n    if (modifiersOrAnnotationOptions && modifiersOrAnnotationOptions instanceof Object && !validationOptions) {\n        validationOptions = modifiersOrAnnotationOptions as ValidationOptions;\n    } else {\n        modifiers = modifiersOrAnnotationOptions as string;\n    }\n\n    return ValidateBy(\n        {\n            name: MATCHES,\n            constraints: [pattern, modifiers],\n            validator: {\n                validate: (value, args) => matches(value, args.constraints[0], args.constraints[0]),\n                defaultMessage: buildMessage(\n                    (eachPrefix, args) => eachPrefix + \"$property must match $constraint1 regular expression\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}