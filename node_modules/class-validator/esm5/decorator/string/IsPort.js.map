{"version": 3, "sources": ["../../src/decorator/string/IsPort.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,CAAC,IAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc;IACjC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAChE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,iBAAqC;IACxD,OAAO,UAAU,CACb;QACI,IAAI,EAAE,OAAO;QACb,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,MAAM,CAAC,KAAK,CAAC,EAAb,CAAa;YACxC,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,0BAA0B,EAAvC,CAAuC,EACvD,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsPort.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport validator from \"validator\";\n\nexport const IS_PORT = \"isPort\";\n\n/**\n * Check if the string is a valid port number.\n */\nexport function isPort(value: unknown): boolean {\n    return typeof value === \"string\" && validator.isPort(value);\n}\n\n/**\n * Check if the string is a valid port number.\n */\nexport function IsPort(validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_PORT,\n            validator: {\n                validate: (value, args) => isPort(value),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be a port\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}