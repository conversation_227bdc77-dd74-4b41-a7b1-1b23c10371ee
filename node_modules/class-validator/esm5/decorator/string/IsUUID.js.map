{"version": 3, "sources": ["../../src/decorator/string/IsUUID.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,SAAS,MAAM,WAAW,CAAC;AAIlC,MAAM,CAAC,IAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc,EAAE,OAAqB;IACxD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACzE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,OAAqB,EAAE,iBAAqC;IAC/E,OAAO,UAAU,CACb;QACI,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAlC,CAAkC;YAC7D,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,2BAA2B,EAAxC,CAAwC,EACxD,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsUUID.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport validator from \"validator\";\n\nexport type UUIDVersion = \"3\" | \"4\" | \"5\" | \"all\" | 3 | 4 | 5;\n\nexport const IS_UUID = \"isUuid\";\n\n/**\n * Checks if the string is a UUID (version 3, 4 or 5).\n * If given value is not a string, then it returns false.\n */\nexport function isUUID(value: unknown, version?: UUIDVersion): boolean {\n    return typeof value === \"string\" && validator.isUUID(value, version);\n}\n\n/**\n * Checks if the string is a UUID (version 3, 4 or 5).\n * If given value is not a string, then it returns false.\n */\nexport function IsUUID(version?: UUIDVersion, validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_UUID,\n            constraints: [version],\n            validator: {\n                validate: (value, args) => isUUID(value, args.constraints[0]),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be an UUID\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}