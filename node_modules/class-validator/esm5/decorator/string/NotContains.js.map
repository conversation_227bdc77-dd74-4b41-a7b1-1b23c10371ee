{"version": 3, "sources": ["../../src/decorator/string/NotContains.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,CAAC,IAAM,YAAY,GAAG,aAAa,CAAC;AAE1C;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,KAAc,EAAE,IAAY;IACpD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACzE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,IAAY,EAAE,iBAAqC;IAC3E,OAAO,UAAU,CACb;QACI,IAAI,EAAE,YAAY;QAClB,WAAW,EAAE,CAAC,IAAI,CAAC;QACnB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAvC,CAAuC;YAClE,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,oDAAoD,EAAjE,CAAiE,EACjF,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "NotContains.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport validator from \"validator\";\n\nexport const NOT_CONTAINS = \"notContains\";\n\n/**\n * Checks if the string does not contain the seed.\n * If given value is not a string, then it returns false.\n */\nexport function notContains(value: unknown, seed: string): boolean {\n    return typeof value === \"string\" && !validator.contains(value, seed);\n}\n\n/**\n * Checks if the string does not contain the seed.\n * If given value is not a string, then it returns false.\n */\nexport function NotContains(seed: string, validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: NOT_CONTAINS,\n            constraints: [seed],\n            validator: {\n                validate: (value, args) => notContains(value, args.constraints[0]),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property should not contain a $constraint1 string\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}