{"version": 3, "sources": ["../../src/decorator/string/IsPhoneNumber.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AAExD,MAAM,CAAC,IAAM,eAAe,GAAG,eAAe,CAAC;AAE/C;;;;;;GAMG;AACH,MAAM,UAAU,aAAa,CAAC,KAAa,EAAE,MAAqB;IAC9D,IAAM,SAAS,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;IAChD,IAAI;QACA,IAAM,QAAQ,GAAG,SAAS,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC/D,IAAM,MAAM,GAAG,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC;KACjB;IAAC,OAAO,KAAK,EAAE;QACZ,WAAW;QACX,OAAO,KAAK,CAAC;KAChB;AACL,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,aAAa,CAAC,MAAqB,EAAE,iBAAqC;IACtF,OAAO,UAAU,CACb;QACI,IAAI,EAAE,eAAe;QACrB,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAzC,CAAyC;YACpE,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,wCAAwC,EAArD,CAAqD,EACrE,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsPhoneNumber.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport { PhoneNumberUtil } from \"google-libphonenumber\";\n\nexport const IS_PHONE_NUMBER = \"isPhoneNumber\";\n\n/**\n * Checks if the string is a valid phone number.\n * @param value the potential phone number string to test\n * @param {string} region 2 characters uppercase country code (e.g. DE, US, CH).\n * If users must enter the intl. prefix (e.g. +41), then you may pass \"ZZ\" or null as region.\n * See [google-libphonenumber, metadata.js:countryCodeToRegionCodeMap on github]{@link https://github.com/ruimarinho/google-libphonenumber/blob/1e46138878cff479aafe2ce62175c6c49cb58720/src/metadata.js#L33}\n */\nexport function isPhoneNumber(value: string, region: string | null): boolean {\n    const phoneUtil = PhoneNumberUtil.getInstance();\n    try {\n        const phoneNum = phoneUtil.parseAndKeepRawInput(value, region);\n        const result = phoneUtil.isValidNumber(phoneNum);\n        return result;\n    } catch (error) {\n        // logging?\n        return false;\n    }\n}\n\n/**\n * Checks if the string is a valid phone number.\n * @param region 2 characters uppercase country code (e.g. DE, US, CH).\n * If users must enter the intl. prefix (e.g. +41), then you may pass \"ZZ\" or null as region.\n * See [google-libphonenumber, metadata.js:countryCodeToRegionCodeMap on github]{@link https://github.com/ruimarinho/google-libphonenumber/blob/1e46138878cff479aafe2ce62175c6c49cb58720/src/metadata.js#L33}\n */\nexport function IsPhoneNumber(region: string | null, validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_PHONE_NUMBER,\n            constraints: [region],\n            validator: {\n                validate: (value, args) => isPhoneNumber(value, args.constraints[0]),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be a valid phone number\",\n                    validationOptions\n                ),\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}