{"version": 3, "sources": ["../../src/decorator/string/IsRFC3339.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,CAAC,IAAM,WAAW,GAAG,WAAW,CAAC;AAEvC;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,KAAc;IACpC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACnE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,iBAAqC;IAC3D,OAAO,UAAU,CACb;QACI,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,SAAS,CAAC,KAAK,CAAC,EAAhB,CAAgB;YAC3C,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,iCAAiC,EAA9C,CAA8C,EAC9D,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsRFC3339.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport validator from \"validator\";\n\nexport const IS_RFC_3339 = \"isRFC3339\";\n\n/**\n * Check if the string is a valid RFC 3339 date.\n * If given value is not a string, then it returns false.\n */\nexport function isRFC3339(value: unknown): boolean {\n    return typeof value === \"string\" && validator.isRFC3339(value);\n}\n\n/**\n * Check if the string is a valid RFC 3339 date.\n * If given value is not a string, then it returns false.\n */\nexport function IsRFC3339(validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_RFC_3339,\n            validator: {\n                validate: (value, args) => isRFC3339(value),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be RFC 3339 date\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}