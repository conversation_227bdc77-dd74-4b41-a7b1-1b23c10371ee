{"version": 3, "sources": ["../../src/decorator/string/IsSemVer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,CAAC,IAAM,UAAU,GAAG,UAAU,CAAC;AAErC;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAc;IACnC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAClE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,iBAAqC;IAC1D,OAAO,UAAU,CACb;QACI,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,QAAQ,CAAC,KAAK,CAAC,EAAf,CAAe;YAC1C,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,uDAAuD,EAApE,CAAoE,EACpF,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsSemVer.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport validator from \"validator\";\n\nexport const IS_SEM_VER = \"isSemVer\";\n\n/**\n * Check if the string is a Semantic Versioning Specification (SemVer).\n * If given value is not a string, then it returns false.\n */\nexport function isSemVer(value: unknown): boolean {\n    return typeof value === \"string\" && validator.isSemVer(value);\n}\n\n/**\n * Check if the string is a Semantic Versioning Specification (SemVer).\n * If given value is not a string, then it returns false.\n */\nexport function IsSemVer(validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_SEM_VER,\n            validator: {\n                validate: (value, args) => isSemVer(value),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be a Semantic Versioning Specification\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}