{"version": 3, "sources": ["../../src/decorator/string/IsOctal.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,CAAC,IAAM,QAAQ,GAAG,SAAS,CAAC;AAElC;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,KAAc;IAClC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,iBAAqC;IACzD,OAAO,UAAU,CACb;QACI,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,OAAO,CAAC,KAAK,CAAC,EAAd,CAAc;YACzC,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,sCAAsC,EAAnD,CAAmD,EACnE,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsOctal.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport validator from \"validator\";\n\nexport const IS_OCTAL = \"isOctal\";\n\n/**\n * Check if the string is a valid octal number.\n * If given value is not a string, then it returns false.\n */\nexport function isOctal(value: unknown): boolean {\n    return typeof value === \"string\" && validator.isOctal(value);\n}\n\n/**\n * Check if the string is a valid octal number.\n * If given value is not a string, then it returns false.\n */\nexport function IsOctal(validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_OCTAL,\n            validator: {\n                validate: (value, args) => isOctal(value),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be valid octal number\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}