{"version": 3, "sources": ["../../src/decorator/string/IsUrl.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,WAAW,MAAM,WAAW,CAAC;AAEpC,MAAM,CAAC,IAAM,MAAM,GAAG,OAAO,CAAC;AAE9B;;;GAGG;AACH,MAAM,UAAU,KAAK,CAAC,KAAa,EAAE,OAAkC;IACnE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC1E,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,KAAK,CAAC,OAAkC,EAAE,iBAAqC;IAC3F,OAAO,UAAU,CACb;QACI,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAjC,CAAiC;YAC5D,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,kCAAkC,EAA/C,CAA+C,EAC/D,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsUrl.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\nimport ValidatorJS from \"validator\";\n\nexport const IS_URL = \"isUrl\";\n\n/**\n * Checks if the string is an url.\n * If given value is not a string, then it returns false.\n */\nexport function isURL(value: string, options?: ValidatorJS.IsURLOptions): boolean {\n    return typeof value === \"string\" && ValidatorJS.isURL(value, options);\n}\n\n/**\n * Checks if the string is an url.\n * If given value is not a string, then it returns false.\n */\nexport function IsUrl(options?: ValidatorJS.IsURLOptions, validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_URL,\n            constraints: [options],\n            validator: {\n                validate: (value, args) => isURL(value, args.constraints[0]),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be an URL address\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}