{"version": 3, "sources": ["../../src/decorator/typechecker/IsArray.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,QAAQ,GAAG,SAAS,CAAC;AAElC;;GAEG;AACH,MAAM,UAAU,OAAO,CAAC,KAAc;IAClC,OAAO,KAAK,YAAY,KAAK,CAAC;AAClC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,OAAO,CAAC,iBAAqC;IACzD,OAAO,UAAU,CACb;QACI,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,OAAO,CAAC,KAAK,CAAC,EAAd,CAAc;YACzC,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,4BAA4B,EAAzC,CAAyC,EACzD,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsArray.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\n\nexport const IS_ARRAY = \"isArray\";\n\n/**\n * Checks if a given value is an array\n */\nexport function isArray(value: unknown): boolean {\n    return value instanceof Array;\n}\n\n/**\n * Checks if a given value is an array\n */\nexport function IsArray(validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_ARRAY,\n            validator: {\n                validate: (value, args) => isArray(value),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be an array\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}