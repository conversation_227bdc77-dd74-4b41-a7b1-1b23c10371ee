{"version": 3, "sources": ["../../src/decorator/typechecker/IsNumber.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,SAAS,GAAG,UAAU,CAAC;AAWpC;;GAEG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAc,EAAE,OAA6B;IAA7B,wBAAA,EAAA,YAA6B;IAClE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC3B,OAAO,KAAK,CAAC;KAChB;IAED,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,CAAC,QAAQ,EAAE;QAC3C,OAAO,OAAO,CAAC,aAAa,CAAC;KAChC;IAED,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACrB,OAAO,OAAO,CAAC,QAAQ,CAAC;KAC3B;IAED,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE;QACxC,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;YACnB,aAAa,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;SACzD;QACD,IAAI,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE;YAC1C,OAAO,KAAK,CAAC;SAChB;KACJ;IAED,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAClC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,QAAQ,CAAC,OAA6B,EAAE,iBAAqC;IAApE,wBAAA,EAAA,YAA6B;IAClD,OAAO,UAAU,CACb;QACI,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAApC,CAAoC;YAC/D,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,oEAAoE,EAAjF,CAAiF,EACjG,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsNumber.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\n\nexport const IS_NUMBER = \"isNumber\";\n\n/**\n * Options to be passed to IsNumber decorator.\n */\nexport interface IsNumberOptions {\n    allowNaN?: boolean;\n    allowInfinity?: boolean;\n    maxDecimalPlaces?: number;\n}\n\n/**\n * Checks if a given value is a number.\n */\nexport function isNumber(value: unknown, options: IsNumberOptions = {}): boolean {\n    if (typeof value !== \"number\") {\n        return false;\n    }\n\n    if (value === Infinity || value === -Infinity) {\n        return options.allowInfinity;\n    }\n\n    if (Number.isNaN(value)) {\n        return options.allowNaN;\n    }\n\n    if (options.maxDecimalPlaces !== undefined) {\n        let decimalPlaces = 0;\n        if ((value % 1) !== 0) {\n            decimalPlaces = value.toString().split(\".\")[1].length;\n        }\n        if (decimalPlaces > options.maxDecimalPlaces) {\n            return false;\n        }\n    }\n\n    return Number.isFinite(value);\n}\n\n/**\n * Checks if a value is a number.\n */\nexport function IsNumber(options: IsNumberOptions = {}, validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_NUMBER,\n            constraints: [options],\n            validator: {\n                validate: (value, args) => isNumber(value, args.constraints[0]),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be a number conforming to the specified constraints\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}