{"version": 3, "sources": ["../../src/decorator/typechecker/IsEnum.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc,EAAE,MAAW;IAC9C,IAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;SACjC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,CAAC,CAAC,EAAT,CAAS,CAAC,CAAC;IACzB,OAAO,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC1C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,MAAc,EAAE,iBAAqC;IACxE,OAAO,UAAU,CACb;QACI,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAlC,CAAkC;YAC7D,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,sCAAsC,EAAnD,CAAmD,EACnE,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsEnum.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\n\nexport const IS_ENUM = \"isEnum\";\n\n/**\n * Checks if a given value is an enum\n */\nexport function isEnum(value: unknown, entity: any): boolean {\n    const enumValues = Object.keys(entity)\n        .map(k => entity[k]);\n    return enumValues.indexOf(value) >= 0;\n}\n\n/**\n * Checks if a given value is an enum\n */\nexport function IsEnum(entity: Object, validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_ENUM,\n            constraints: [entity],\n            validator: {\n                validate: (value, args) => isEnum(value, args.constraints[0]),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be a valid enum value\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}