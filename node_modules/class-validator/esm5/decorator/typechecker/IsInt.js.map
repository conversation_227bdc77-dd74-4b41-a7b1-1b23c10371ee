{"version": 3, "sources": ["../../src/decorator/typechecker/IsInt.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,MAAM,GAAG,OAAO,CAAC;AAE9B;;GAEG;AACH,MAAM,UAAU,KAAK,CAAC,GAAY;IAC9B,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC5D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,KAAK,CAAC,iBAAqC;IACvD,OAAO,UAAU,CACb;QACI,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,KAAK,CAAC,KAAK,CAAC,EAAZ,CAAY;YACvC,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,qCAAqC,EAAlD,CAAkD,EAClE,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsInt.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\n\nexport const IS_INT = \"isInt\";\n\n/**\n * Checks if value is an integer.\n */\nexport function isInt(val: unknown): boolean {\n    return typeof val === \"number\" && Number.isInteger(val);\n}\n\n/**\n * Checks if value is an integer.\n */\nexport function IsInt(validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_INT,\n            validator: {\n                validate: (value, args) => isInt(value),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be an integer number\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}