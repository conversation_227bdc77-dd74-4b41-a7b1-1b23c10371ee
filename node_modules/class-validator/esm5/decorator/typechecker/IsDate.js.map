{"version": 3, "sources": ["../../src/decorator/typechecker/IsDate.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc;IACjC,OAAO,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;AAC5D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,iBAAqC;IACxD,OAAO,UAAU,CACb;QACI,IAAI,EAAE,OAAO;QACb,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,MAAM,CAAC,KAAK,CAAC,EAAb,CAAa;YACxC,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,mCAAmC,EAAhD,CAAgD,EAChE,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsDate.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\n\nexport const IS_DATE = \"isDate\";\n\n/**\n * Checks if a given value is a number.\n */\nexport function isDate(value: unknown): boolean {\n    return value instanceof Date && !isNaN(value.getTime());\n}\n\n/**\n * Checks if a value is a number.\n */\nexport function IsDate(validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_DATE,\n            validator: {\n                validate: (value, args) => isDate(value),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be a Date instance\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}