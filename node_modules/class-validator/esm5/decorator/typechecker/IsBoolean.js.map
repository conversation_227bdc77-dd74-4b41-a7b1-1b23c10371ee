{"version": 3, "sources": ["../../src/decorator/typechecker/IsBoolean.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,UAAU,GAAG,WAAW,CAAC;AAEtC;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,KAAc;IACpC,OAAO,KAAK,YAAY,OAAO,IAAI,OAAO,KAAK,KAAK,SAAS,CAAC;AAClE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,iBAAqC;IAC3D,OAAO,UAAU,CACb;QACI,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,SAAS,CAAC,KAAK,CAAC,EAAhB,CAAgB;YAC3C,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,mCAAmC,EAAhD,CAAgD,EAChE,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsBoolean.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\n\nexport const IS_BOOLEAN = \"isBoolean\";\n\n/**\n * Checks if a given value is a number.\n */\nexport function isBoolean(value: unknown): boolean {\n    return value instanceof Boolean || typeof value === \"boolean\";\n}\n\n/**\n * Checks if a value is a number.\n */\nexport function IsBoolean(validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_BOOLEAN,\n            validator: {\n                validate: (value, args) => isBoolean(value),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be a boolean value\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}