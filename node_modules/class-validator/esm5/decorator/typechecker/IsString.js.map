{"version": 3, "sources": ["../../src/decorator/typechecker/IsString.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,SAAS,GAAG,UAAU,CAAC;AAEpC;;EAEE;AACF,MAAM,UAAU,QAAQ,CAAC,KAAc;IACpC,OAAO,KAAK,YAAY,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;AAC/D,CAAC;AAED;;EAEE;AACF,MAAM,UAAU,QAAQ,CAAC,iBAAqC;IAC1D,OAAO,UAAU,CACb;QACI,IAAI,EAAE,SAAS;QACf,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,QAAQ,CAAC,KAAK,CAAC,EAAf,CAAe;YAC1C,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,4BAA4B,EAAzC,CAAyC,EACzD,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsString.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\n\nexport const IS_STRING = \"isString\";\n\n/**\n* Checks if a given value is a real string.\n*/\nexport function isString(value: unknown): value is string {\n   return value instanceof String || typeof value === \"string\";\n}\n\n/**\n* Checks if a given value is a real string.\n*/\nexport function IsString(validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_STRING,\n            validator: {\n                validate: (value, args) => isString(value),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be a string\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}