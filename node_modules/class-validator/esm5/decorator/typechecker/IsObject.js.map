{"version": 3, "sources": ["../../src/decorator/typechecker/IsObject.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,SAAS,GAAG,UAAU,CAAC;AAEpC;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAc;IACnC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAChH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,iBAAqC;IAC1D,OAAO,UAAU,CACb;QACI,IAAI,EAAE,SAAS;QACf,SAAS,EAAE;YACP,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,QAAQ,CAAC,KAAK,CAAC,EAAf,CAAe;YAC1C,cAAc,EAAE,YAAY,CACxB,UAAC,UAAU,IAAK,OAAA,UAAU,GAAG,6BAA6B,EAA1C,CAA0C,EAC1D,iBAAiB,CACpB;SACJ;KACJ,EACD,iBAAiB,CACpB,CAAC;AACN,CAAC", "file": "IsObject.js", "sourcesContent": ["import { ValidationOptions } from \"../ValidationOptions\";\nimport { buildMessage, ValidateBy } from \"../common/ValidateBy\";\n\nexport const IS_OBJECT = \"isObject\";\n\n/**\n * Checks if the value is valid Object.\n * Returns false if the value is not an object.\n */\nexport function isObject(value: unknown): value is object {\n    return value != null && (typeof value === \"object\" || typeof value === \"function\") && !Array.isArray(value);\n}\n\n/**\n * Checks if the value is valid Object.\n * Returns false if the value is not an object.\n */\nexport function IsObject(validationOptions?: ValidationOptions): PropertyDecorator {\n    return ValidateBy(\n        {\n            name: IS_OBJECT,\n            validator: {\n                validate: (value, args) => isObject(value),\n                defaultMessage: buildMessage(\n                    (eachPrefix) => eachPrefix + \"$property must be an object\",\n                    validationOptions\n                )\n            }\n        },\n        validationOptions\n    );\n}\n"], "sourceRoot": "../.."}