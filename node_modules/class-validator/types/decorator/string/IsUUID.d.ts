import { ValidationOptions } from "../ValidationOptions";
export declare type UUIDVersion = "3" | "4" | "5" | "all" | 3 | 4 | 5;
export declare const IS_UUID = "isUuid";
/**
 * Checks if the string is a UUID (version 3, 4 or 5).
 * If given value is not a string, then it returns false.
 */
export declare function isUUID(value: unknown, version?: UUIDVersion): boolean;
/**
 * Checks if the string is a UUID (version 3, 4 or 5).
 * If given value is not a string, then it returns false.
 */
export declare function IsUUID(version?: UUIDVersion, validationOptions?: ValidationOptions): PropertyDecorator;
