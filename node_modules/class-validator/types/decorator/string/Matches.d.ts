import { ValidationOptions } from "../ValidationOptions";
export declare const MATCHES = "matches";
/**
 * Checks if string matches the pattern. Either matches('foo', /foo/i).
 * If given value is not a string, then it returns false.
 */
export declare function matches(value: string, pattern: RegExp): boolean;
export declare function matches(value: string, pattern: string, modifiers: string): boolean;
/**
 * Checks if string matches the pattern. Either matches('foo', /foo/i)
 * If given value is not a string, then it returns false.
 */
export declare function Matches(pattern: RegExp, validationOptions?: ValidationOptions): PropertyDecorator;
export declare function Matches(pattern: string, modifiers?: string, validationOptions?: ValidationOptions): PropertyDecorator;
