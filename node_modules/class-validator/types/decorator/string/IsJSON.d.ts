import { ValidationOptions } from "../ValidationOptions";
export declare const IS_JSON = "isJson";
/**
 * Checks if the string is valid JSON (note: uses JSON.parse).
 * If given value is not a string, then it returns false.
 */
export declare function isJSON(value: unknown): boolean;
/**
 * Checks if the string is valid JSON (note: uses JSON.parse).
 * If given value is not a string, then it returns false.
 */
export declare function IsJSON(validationOptions?: ValidationOptions): PropertyDecorator;
